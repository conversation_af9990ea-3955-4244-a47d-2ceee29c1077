.draggableItem {
  margin-bottom: 8px;
  transition: all 0.2s ease;
  cursor: grab;

  &.dragging {
    opacity: 0.5;
    cursor: grabbing;
    transform: rotate(2deg);
    transition: none;

    .dragIcon {
      color: #1890ff;
    }
  }

  &.dropTarget {
    .dividerContainer {
      border: 2px solid #1890ff;
      background: #e6f7ff;
    }

    .sceneCard {
      border: 2px solid #1890ff;
      background: #e6f7ff;
    }
  }
}

.dividerContainer {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 80px;

  // background: #f0f8ff;
  border: 1px dashed #1890ff;
  border-radius: 4px;
  transition: all 0.2s ease;

  .dragIcon {
    margin-right: 8px;
    font-size: 16px;
    color: #1890ff;
    transition: all 0.2s ease;
    cursor: grab;

    &.dragging {
      cursor: grabbing;
    }
  }

  .dividerContent {
    flex: 1;
    text-align: center;

    .dividerText {
      font-weight: 500;
      color: #1890ff;
    }
  }
}

.sceneCard {
  transition: all 0.2s ease;

  &.verified {
    border: 1px solid #52c41a;
    background-color: #f6ffed;
  }

  &.dragging {
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
  }

  &.dropTarget {
    border: 2px solid #1890ff;
    background: #e6f7ff;
  }

  .sceneHeader {
    justify-content: space-between;
    width: 100%;

    .sceneInfo {
      .dragIcon {
        font-size: 16px;
        color: #999;
        transition: all 0.2s ease;
        cursor: grab;

        &.dragging {
          color: #1890ff;
          cursor: grabbing;
        }
      }

      .sceneNumber {
        font-weight: 600;
        color: #262626;
      }

      .planType {
        font-weight: 500;
      }

      .sortNumber {
        color: #8c8c8c;
      }
    }

    .verificationStatus {
      font-weight: 500;
      color: #52c41a;

      .closeIcon {
        margin-left: 8px;
        font-size: 12px;
        color: #ff4d4f;
        cursor: pointer;

        &:hover {
          color: #d9363e;
        }
      }
    }
  }

  .sceneDetails {
    width: 100%;

    .detailRow {
      justify-content: space-between;
      width: 100%;

      .detailItem {
        .detailLabel {
          margin-right: 4px;
          color: #8c8c8c;
        }

        .detailValue {
          color: #262626;
        }
      }
    }
  }
}

.sceneListContainer {
  overflow-y: auto;
  padding-right: 8px;
  height: calc(100vh - 270px);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #c1c1c1;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.emptyState {
  padding: 40px 20px;
  text-align: center;
  color: #8c8c8c;

  .emptyIcon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #d9d9d9;
  }

  .emptyText {
    margin-bottom: 8px;
    font-size: 16px;
  }

  .emptyDescription {
    font-size: 14px;
    color: #bfbfbf;
  }
}
