import { But<PERSON>, Card, Flex, Spin, Typography, message, Popconfirm, Tabs, Input } from 'antd'
import { DeleteOutlined, SearchOutlined } from '@ant-design/icons'
import React, { useEffect, useState, useMemo } from 'react'
import useProjectListStore, { ISceneVenueInfo, IPrProductionVenueInfo } from '../../list/store'
import VenueDetail from '../../../venue/list/components/Detail'

interface VenueExplorationTabProps {
  productionId: number
}

const VenueExplorationTab: React.FC<VenueExplorationTabProps> = ({ productionId }) => {
  const { getScenePlanVenueAndInfo, updateVenueSelectionType, deleteProductionVenueInfo } = useProjectListStore()
  const [loading, setLoading] = useState(false)
  const [venueDataList, setVenueDataList] = useState<ISceneVenueInfo[]>([])
  const [updatingVenueId, setUpdatingVenueId] = useState<number | null>(null)
  const [deletingVenueId, setDeletingVenueId] = useState<number | null>(null)

  // 筛选相关状态
  const [filterText, setFilterText] = useState<string>('')

  // 场地详情相关状态
  const [venueDetailOpen, setVenueDetailOpen] = useState(false)
  const [selectedVenueId, setSelectedVenueId] = useState<number | undefined>()

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const result = await getScenePlanVenueAndInfo(productionId)
      setVenueDataList(result || [])
    } catch (error) {
      console.error('获取场景勘探数据失败:', error)
      message.error('获取场景勘探数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [productionId])

  // 刷新数据
  const handleRefresh = () => {
    loadData()
  }

  // 处理场地名称点击
  const handleVenueNameClick = (venueInfo: IPrProductionVenueInfo) => {
    if (venueInfo.venueId) {
      setSelectedVenueId(venueInfo.venueId)
      setVenueDetailOpen(true)
    }
  }

  // 关闭场地详情
  const handleVenueDetailClose = () => {
    setVenueDetailOpen(false)
    setSelectedVenueId(undefined)
  }

  // 处理设置为主选
  const handleSetAsPrimary = async (venueInfo: IPrProductionVenueInfo) => {
    if (!venueInfo.id) {
      message.error('场地信息不完整')
      return
    }

    setUpdatingVenueId(venueInfo.id)
    try {
      const success = await updateVenueSelectionType(venueInfo.id)
      if (success) {
        message.success('设置为主选成功')
        // 刷新数据
        loadData()
      } else {
        message.error('设置为主选失败')
      }
    } catch (error) {
      console.error('设置为主选失败:', error)
      message.error('设置为主选失败')
    } finally {
      setUpdatingVenueId(null)
    }
  }

  // 处理删除场地场景关联
  const handleDeleteVenueInfo = async (venueInfo: IPrProductionVenueInfo) => {
    if (!venueInfo.id) {
      message.error('场地信息不完整')
      return
    }

    setDeletingVenueId(venueInfo.id)
    try {
      const success = await deleteProductionVenueInfo(venueInfo.id)
      if (success) {
        message.success('删除场地关联成功')
        // 刷新数据
        loadData()
      } 
    } catch (error) {
      console.error('删除场地关联失败:', error)
      message.error('删除场地关联失败')
    } finally {
      setDeletingVenueId(null)
    }
  }

  // 使用useMemo根据mainVenue分组数据并支持筛选
  const tabsData = useMemo(() => {
    if (!venueDataList.length) return []

    // 按mainVenue分组
    const groupedData = venueDataList.reduce((acc, sceneInfo) => {
      const mainVenue = sceneInfo.mainVenue || '未分类场景'

      if (!acc[mainVenue]) {
        acc[mainVenue] = []
      }

      acc[mainVenue].push(sceneInfo)

      return acc
    }, {} as Record<string, ISceneVenueInfo[]>)

    // 转换为tabs数据格式并应用筛选
    let filteredEntries = Object.entries(groupedData)

    if (filterText.trim()) {
      filteredEntries = filteredEntries.filter(([mainVenue, scenes]) => {
        // 筛选主场景名称
        const mainVenueMatch = mainVenue.toLowerCase().includes(filterText.toLowerCase())

        // 筛选分场景名称
        const subVenueMatch = scenes.some(scene =>
          scene.venue?.toLowerCase().includes(filterText.toLowerCase())
        )

        // 筛选关联场地名称
        const venueNameMatch = scenes.some(scene =>
          scene.venueInfos?.some(venue =>
            venue.venueName?.toLowerCase().includes(filterText.toLowerCase())
          )
        )

        return mainVenueMatch || subVenueMatch || venueNameMatch
      })
    }

    return filteredEntries.map(([mainVenue, scenes], index) => ({
      key: `tab-${index}`,
      label: mainVenue,
      children: scenes,
    }))
  }, [venueDataList, filterText])

  // 渲染场地信息卡片
  const renderVenueCard = (venueInfo: IPrProductionVenueInfo) => (
    <Card size="small" key={venueInfo.id}>
      <Flex vertical gap={8}>
        <Flex justify="space-between" align="center">
          <Typography.Text
            strong
            style={{ cursor: 'pointer', color: '#1890ff' }}
            onClick={() => handleVenueNameClick(venueInfo)}
          >
            {venueInfo.venueName}
          </Typography.Text>

          <Popconfirm
            title="确认删除"
            description="确定要删除这个场地关联吗？"
            onConfirm={() => handleDeleteVenueInfo(venueInfo)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              loading={deletingVenueId === venueInfo.id}
              danger
            />
          </Popconfirm>
        </Flex>
        <Flex align="center" gap={8}>
          <Typography.Text type="secondary">
            {venueInfo.selectionType === 1 ? '主选' : '备选'}
          </Typography.Text>
          {venueInfo.selectionType !== 1 && (
            <Button
              size="small"
              type="link"
              loading={updatingVenueId === venueInfo.id}
              onClick={() => handleSetAsPrimary(venueInfo)}
            >
              设为主选
            </Button>
          )}
        </Flex>
        {/* {venueInfo.mainSceneName && (
            <Typography.Text>
              <span style={{ color: '#666' }}>主场景：</span>
              {venueInfo.mainSceneName}
            </Typography.Text>
          )}

          {venueInfo.subSceneName && (
            <Typography.Text>
              <span style={{ color: '#666' }}>子场景：</span>
              {venueInfo.subSceneName}
            </Typography.Text>
          )} */}

        {venueInfo.subVenueName && (
          <Typography.Text>
            <span style={{ color: '#666' }}>子场地：</span>
            {venueInfo.subVenueName}
          </Typography.Text>
        )}

        {venueInfo.updateTime && (
          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
            更新时间：{venueInfo.updateTime}
          </Typography.Text>
        )}
      </Flex>
    </Card>
  )

  // 渲染场景信息卡片
  const renderSceneCard = (sceneInfo: ISceneVenueInfo, index: number) => (
    <Card
      key={index}
      title={`${sceneInfo.venue || '场景信息' + index + 1}`}
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Flex gap={16}>
        {/* 场景基本信息 */}
        <Flex vertical gap={8}>


          {sceneInfo.pageCount !== undefined && (
            <Typography.Text>
              <span style={{ color: '#666' }}>页数：</span>
              {sceneInfo.pageCount}
            </Typography.Text>
          )}

          {sceneInfo.count !== undefined && (
            <Typography.Text>
              <span style={{ color: '#666' }}>场次：</span>
              {sceneInfo.count}
            </Typography.Text>
          )}
        </Flex>

        {/* 关联场地列表 */}
        {sceneInfo.venueInfos && sceneInfo.venueInfos.length > 0 ?
          sceneInfo.venueInfos.map(item => {
            return renderVenueCard(item)
          })
          : null}
      </Flex>
    </Card>
  )

  return (
    <>
      <Spin spinning={loading}>
        <Flex vertical gap={16}>
          <Flex justify="space-between" align="center">
            <Typography.Title level={5} style={{ margin: 0 }}>
              场景勘探信息
            </Typography.Title>
            <Flex gap={12} align="center">
              <Input
                placeholder="筛选场景或场地..."
                prefix={<SearchOutlined />}
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
                allowClear
                style={{ width: 200 }}
              />
              <Button onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
            </Flex>
          </Flex>

          {tabsData.length > 0 ? (
            <Tabs
              type="card"
              items={tabsData.map(tab => ({
                key: tab.key,
                label: tab.label,
                children: (
                  <Flex vertical gap={16}>
                    {tab.children.map((sceneInfo, index) => renderSceneCard(sceneInfo, index))}
                  </Flex>
                ),
              }))}
            />
          ) : (
            <Card>
              <Flex vertical gap={8} align="center" style={{ padding: '20px 0' }}>
                <Typography.Text type="secondary">
                  {filterText.trim() ? '未找到匹配的场景或场地' : '暂无场景勘探数据'}
                </Typography.Text>
                {filterText.trim() && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => setFilterText('')}
                  >
                    清除筛选条件
                  </Button>
                )}
              </Flex>
            </Card>
          )}
        </Flex>
      </Spin>

      {/* 场地详情抽屉 */}
      {venueDetailOpen ? <VenueDetail
        open={venueDetailOpen}
        venueId={selectedVenueId}
        isShow={true}
        onClose={handleVenueDetailClose}
      /> : null}
    </>
  )
}

export default VenueExplorationTab
